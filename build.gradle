buildscript {
    ext {
        springBootVersion = '2.1.0.RELEASE'
    }
    repositories {
        mavenCentral()
        maven {
            url "https://plugins.gradle.org/m2/"
        }
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
        classpath "io.freefair.gradle:lombok-plugin:6.1.0"
        classpath "io.spring.gradle:dependency-management-plugin:0.5.2.RELEASE"
    }
}

plugins {
    id "org.sonarqube" version "2.6"
}

apply plugin: 'java'
apply plugin: 'eclipse'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'jacoco'
apply plugin: "io.spring.dependency-management"

jacoco {
  toolVersion = "0.8.6"
  reportsDir = file("$buildDir/customJacocoReportDir")
}
jacocoTestReport {
  reports {
    xml.enabled false
    csv.enabled false
    html.destination file("${buildDir}/jacocoHtml")
  }
}

group = 'com.btpnsyariah.agendaku'
sourceCompatibility = 1.8

repositories {
    mavenCentral()
    maven { url "https://repo.spring.io/milestone" }
}

ext['springCloudVersion'] = 'Greenwich.RC2'

dependencies {
    implementation("io.minio:minio:8.2.1")
//    implementation("com.squareup.okhttp3:okhttp:4.8.1")
    implementation('org.springframework.boot:spring-boot-starter-web')
    implementation('org.springframework.boot:spring-boot-starter-actuator')
    implementation group: 'com.microsoft.sqlserver', name: 'mssql-jdbc', version: '6.1.0.jre8'

    implementation('org.springframework.boot:spring-boot-starter-data-jpa')
    implementation group: 'org.springframework.boot', name: 'spring-boot-starter-logging'
    implementation 'org.springdoc:springdoc-openapi-ui:1.6.4'
    implementation group: 'org.springframework.kafka', name: 'spring-kafka', version: '2.2.11.RELEASE'

    implementation('ch.qos.logback.contrib:logback-json-classic:0.1.5')
    implementation('ch.qos.logback.contrib:logback-jackson:0.1.5')

    implementation group: 'org.flywaydb', name: 'flyway-core', version: '5.2.1'

    // https://mvnrepository.com/artifact/mysql/mysql-connector-java
    implementation group: 'mysql', name: 'mysql-connector-java', version: '8.0.33'

    implementation group: 'org.apache.poi', name: 'poi', version: '3.15'
    implementation group: 'org.apache.poi', name: 'poi-ooxml', version: '3.15'
    implementation group: 'commons-io', name: 'commons-io', version: '2.6'
    implementation 'org.projectlombok:lombok:1.18.22'
    compile group: 'joda-time', name: 'joda-time', version: '2.9.9'

    compile group: 'org.json', name: 'json', version: '20180813'
    compile 'javax.xml.bind:jaxb-api:2.1'
    compile 'org.docx4j:docx4j-core:8.2.9'
    compile 'org.docx4j:docx4j-JAXB-Internal:8.3.8'
    compile 'org.docx4j:docx4j-JAXB-ReferenceImpl:8.2.9'
    compile 'org.docx4j:docx4j-openxml-objects:8.2.9'
    compile 'org.docx4j.org.apache:xalan-interpretive:8.0.0'
    compile 'org.docx4j.org.apache:xalan-serializer:8.0.0'
    compile group: 'org.docx4j', name: 'docx4j-export-fo', version: '6.1.0'

    compile group: 'fr.opensagres.xdocreport', name: 'org.apache.poi.xwpf.converter.core', version: '1.0.6'
    compile group: 'fr.opensagres.xdocreport', name: 'org.apache.poi.xwpf.converter.pdf', version: '1.0.6'
    compile group: 'fr.opensagres.xdocreport', name: 'org.apache.poi.xwpf.converter.xhtml', version: '1.0.6'
    compile group: 'fr.opensagres.xdocreport', name: 'fr.opensagres.xdocreport.itext.extension', version: '2.0.0'
    compile group: 'org.springframework.boot', name: 'spring-boot-starter-thymeleaf'

    //runtimeOnly('com.microsoft.sqlserver:mssql-jdbc')
    testImplementation('org.springframework.boot:spring-boot-starter-test')
    testCompile group: 'org.springframework.kafka', name: 'spring-kafka-test', version: '2.2.11.RELEASE'

    compile 'org.apache.httpcomponents:httpclient:4.5.13'

    compileOnly 'org.projectlombok:lombok:1.18.20'
    annotationProcessor 'org.projectlombok:lombok:1.18.20'

    testCompileOnly 'org.projectlombok:lombok:1.18.20'
    testAnnotationProcessor 'org.projectlombok:lombok:1.18.20'

    compile 'org.springframework.cloud:spring-cloud-starter-sleuth'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

configurations {
    all {
        exclude group: 'org.apache.logging.log4j', module: 'log4j-slf4j'
        exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    }
}

sonarqube{
    properties {
        property "sonar.projectKey", "<key> "
        property "sonar.host.url", "<host:port>"
        property "sonar.login", "<loginToken>"
        property "sonar.tests", "src/test/java"
        property "sonar.exclusions", "**/agendaku/util/**"
    }
}
